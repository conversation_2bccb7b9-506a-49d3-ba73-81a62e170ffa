import 'dotenv/config';

export default {
  name: "QWRM",
  slug: "signup-expo",
  version: "1.0.0",
  owner: "blood-and-treasure",
  orientation: "portrait",
  icon: "./assets/app/icon_1024x1024.png",
  scheme: "qwrm",
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  jsEngine: "jsc",
  splash: {
    backgroundColor: "#202326"
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.signupsheet",
    deploymentTarget: "15.0", // Ensure this is set to a version you support
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      NSPhotoLibraryUsageDescription: "This app needs access to your photo library to allow you to upload event and profile images."
    }
  },
  android: {
    versionCode: 8,
    adaptiveIcon: {
      foregroundImage: "./assets/app/adaptive-icon.png",
      backgroundColor: "#00788D"
    },
    package: "com.signupsheet"
  },
  web: {
    bundler: "metro",
    output: "static",
    favicon: "./assets/app/favicon.png"
  },
  plugins: [
    "expo-router",
    "expo-asset",
    [
      "expo-notifications",
      {
        icon: "./assets/app/notification-icon.png",
        color: "#00788D",
        sounds: ["./assets/sounds/notification.wav"],
        androidMode: "default",
        androidCollapsedTitle: "{{unread_count}} new interactions"
      }
    ],
    [
      "expo-font",
      {
        fonts: [
          "./assets/fonts/WorkSans-Regular.ttf",
          "./assets/fonts/WorkSans-Light.ttf",
          "./assets/fonts/WorkSans-Medium.ttf",
          "./assets/fonts/WorkSans-SemiBold.ttf",
          "./assets/fonts/WorkSans-Bold.ttf",
          "./assets/fonts/WorkSans-ExtraBold.ttf"
        ],
        android: {
          fonts: [
            "./assets/fonts/WorkSans-Regular.ttf",
            "./assets/fonts/WorkSans-Light.ttf",
            "./assets/fonts/WorkSans-Medium.ttf",
            "./assets/fonts/WorkSans-SemiBold.ttf",
            "./assets/fonts/WorkSans-Bold.ttf",
            "./assets/fonts/WorkSans-ExtraBold.ttf"
          ]
        },
        ios: {
          fonts: [
            "./assets/fonts/WorkSans-Regular.ttf",
            "./assets/fonts/WorkSans-Light.ttf",
            "./assets/fonts/WorkSans-Medium.ttf",
            "./assets/fonts/WorkSans-SemiBold.ttf",
            "./assets/fonts/WorkSans-Bold.ttf",
            "./assets/fonts/WorkSans-ExtraBold.ttf"
          ]
        }
      }
    ]
  ],
  experiments: {
    typedRoutes: true
  },
  extra: {
    router: {
      origin: false
    },
    eas: {
      projectId: "50eb4be8-5d7c-45b2-937c-54d14dc96076"
    },
    googleAndroidClientId: process.env.GOOGLE_ANDROID_OAUTH_CLIENT_ID,
    googleIosClientId: process.env.GOOGLE_IOS_OAUTH_CLIENT_ID,
    mixpanelToken: process.env.MIXPANEL_TOKEN,
    mixpanelSecret: process.env.MIXPANEL_SECRET,
  },
  "build": {
    "production": {
      "credentialsSource": "local"
    }
  }
};
