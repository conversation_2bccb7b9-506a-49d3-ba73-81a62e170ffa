{"name": "QWRM", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@expo/ngrok": "^4.1.0", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/native": "^7.0.14", "axios": "^1.8.4", "class-variance-authority": "^0.7.0", "clsx": "2.1.1", "dotenv": "^16.5.0", "expo": "~52.0.47", "expo-auth-session": "^6.0.3", "expo-constants": "^17.0.8", "expo-contacts": "^14.0.5", "expo-dev-client": "~5.0.19", "expo-device": "^7.0.3", "expo-file-system": "^18.0.12", "expo-font": "~13.0.4", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.0.5", "expo-notifications": "^0.29.14", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "lucide-react-native": "^0.453.0", "mixpanel-react-native": "^3.1.2", "moment": "^2.30.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.20.2", "react-native-picker-select": "^9.3.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "tailwind-merge": "^3.1.0", "tailwind-rn": "^4.2.0", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "~5.3.3"}, "private": true}